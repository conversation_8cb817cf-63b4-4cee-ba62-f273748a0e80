import asyncio
import json

from bilibili_api import Credential, video

from logger import logger


async def get_all_video_info(bvid):
    video_instance = video.Video(bvid=bvid)
    try:
        res = await video_instance.get_info()
        if res is not None:
            # "stat": {
            #     "aid": 113547728257864,
            #     "view": 86607,
            #     "danmaku": 93,
            #     "reply": 376,
            #     "favorite": 753,
            #     "coin": 1296,
            #     "share": 152,
            #     "now_rank": 0,
            #     "his_rank": 0,
            #     "like": 6225,
            #     "dislike": 0,
            #     "evaluation": "",
            #     "vt": 0
            # },
            stat = res["stat"]

            if "honor" not in res["honor_reply"]:
                return {
                    "honor_short": "",
                    "honor_count": 0,
                    "honor": [],
                    "stat": stat,
                }
            honor = res["honor_reply"]["honor"]
            res = ""
            if honor == []:
                return {
                    "honor_short": res,
                    "honor_count": 0,
                    "honor": [],
                    "stat": stat,
                }
            else:
                i = 0
                for h in honor:
                    res += h["desc"] + ","
                    i += 1
                return {
                    "honor_short": res,
                    "honor_count": i,
                    "honor": json.dumps(honor, ensure_ascii=False),
                    "stat": stat,
                }
    except Exception as e:
        logger.error(e)
        return {
            "honor_short": "",
            "honor_count": 0,
            "honor": [],
            "stat": {},
        }


async def get_video_ai_conclusion(bvid, credential):
    """

    Returns:
    {
        "code": 0,
        "model_result": {
            "result_type": 0,
            "summary": 0,
            "outline": 0,
            "subtitle": 0,
        },
        "stid": "3303183034006860469",
        "status": 0,
        "like_num": 0,
        "dislike_num": 1,
    }
    """

    try:
        # Temporarily disable AI conclusion due to bilibili_api bug
        # The bug is: bilibili_api.utils.network.Api() got multiple values for keyword argument 'wbi'
        # This happens because wbi=True is passed explicitly while wbi might already be in params
        logger.warning("AI conclusion temporarily disabled due to bilibili_api wbi parameter bug")
        return {
            "code": 1,
            "model_result": {
                "result_type": 0,
                "summary": "AI总结功能暂时不可用，bilibili_api库存在wbi参数重复传递的bug",
                "outline": None,
                "subtitle": None,
            },
            "stid": "0",
            "status": 0,
            "like_num": 0,
            "dislike_num": 0,
        }
    except Exception as e:
        logger.error(e)
        return {
            "code": 1,
            "model_result": [],
        }

async def main():
    from server.base.cookie_manager import get_cookie_field
    c = Credential(
        sessdata=get_cookie_field("user", "SESSDATA"),
        bili_jct=get_cookie_field("user", "bili_jct"),
        buvid3=get_cookie_field("user", "buvid3"),
        dedeuserid=get_cookie_field("user", "DedeUserID"),
        buvid4=get_cookie_field("user", "buvid4"),
    )
    bvid = "BV1L94y1H7CV"
    res = await get_video_ai_conclusion(bvid, c)
    print(str(res))


if __name__ == "__main__":
    asyncio.run(main())